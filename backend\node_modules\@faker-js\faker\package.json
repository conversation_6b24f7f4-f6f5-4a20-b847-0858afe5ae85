{"name": "@faker-js/faker", "version": "9.8.0", "description": "Generate massive amounts of fake contextual data", "keywords": ["faker", "faker.js", "fakerjs", "faker-js", "fake data generator", "fake data", "fake-data", "fake-generator", "fake-data-generator", "fake content generator", "fake contextual data generator", "fake contextual data"], "homepage": "https://fakerjs.dev", "bugs": {"url": "https://github.com/faker-js/faker/issues"}, "repository": {"type": "git", "url": "git+https://github.com/faker-js/faker.git"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/fakerjs"}], "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./locale/*": {"require": {"types": "./dist/locale/*.d.cts", "default": "./dist/locale/*.cjs"}, "default": {"types": "./dist/locale/*.d.ts", "default": "./dist/locale/*.js"}}, "./package.json": "./package.json"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "typesVersions": {">=5.0": {".": ["./dist/index.d.ts"], "locale/*": ["./dist/locale/*.d.ts"]}}, "files": ["CHANGELOG.md", "dist"], "devDependencies": {"@eslint/compat": "1.2.9", "@eslint/js": "9.26.0", "@stylistic/eslint-plugin": "4.2.0", "@types/node": "22.14.1", "@types/sanitize-html": "2.15.0", "@types/semver": "7.7.0", "@types/validator": "13.15.0", "@vitest/coverage-v8": "3.1.3", "@vitest/eslint-plugin": "1.1.44", "@vitest/ui": "3.1.3", "@vueuse/core": "13.1.0", "commit-and-tag-version": "12.5.1", "cypress": "14.3.0", "eslint": "9.26.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-file-progress": "3.0.2", "eslint-plugin-jsdoc": "50.6.14", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-unicorn": "59.0.1", "jiti": "2.4.2", "npm-run-all2": "7.0.2", "prettier": "3.5.3", "prettier-plugin-organize-imports": "4.1.0", "prettier-plugin-packagejson": "2.5.10", "rimraf": "5.0.10", "sanitize-html": "2.15.0", "semver": "7.7.1", "ts-morph": "25.0.1", "tsup": "8.4.0", "tsx": "4.19.3", "typescript": "5.8.3", "typescript-eslint": "8.32.0", "validator": "13.15.0", "vitepress": "1.6.3", "vitest": "3.1.3", "vue": "3.5.13", "vue-tsc": "2.2.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "scripts": {"clean": "rimraf coverage .eslintcache dist docs/.vitepress/cache docs/.vitepress/dist node_modules", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:code": "tsup-node", "build": "run-s build:clean build:code", "generate": "run-s generate:locales generate:api-docs", "generate:api-docs": "tsx ./scripts/apidocs.ts", "generate:locales": "tsx ./scripts/generate-locales.ts", "docs:build": "run-s generate:api-docs docs:build:embedded docs:build:run", "docs:build:embedded": "tsup-node --entry.faker src/index.ts --format esm --outDir docs/public --no-dts --no-clean", "docs:build:run": "vitepress build docs", "docs:build:ci": "run-s build docs:build", "docs:dev": "run-s generate:api-docs docs:build:embedded docs:dev:run", "docs:dev:run": "vitepress dev docs", "docs:serve": "vitepress serve docs --port 5173", "docs:diff": "tsx ./scripts/diff.ts", "docs:test:e2e:ci": "run-s docs:build:ci docs:test:e2e:install docs:test:e2e:run", "docs:test:e2e:install": "cypress install", "docs:test:e2e:run": "run-p --race docs:serve \"cypress run\"", "docs:test:e2e:open": "run-p --race docs:serve \"cypress open\"", "format": "prettier --cache --write .", "lint": "eslint --cache --cache-strategy content .", "ts-check": "tsc", "test": "vitest", "test:update-snapshots": "vitest run -u", "coverage": "vitest run --coverage", "integration-test": "vitest -c vitest.it-config.ts", "cypress": "cypress", "release": "commit-and-tag-version --commit-all", "preflight": "pnpm install && run-s generate format lint build test:update-snapshots ts-check"}}