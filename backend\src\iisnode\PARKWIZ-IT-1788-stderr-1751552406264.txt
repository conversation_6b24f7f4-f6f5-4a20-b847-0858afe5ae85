(node:1788) [DEP0005] DeprecationWarning: <PERSON><PERSON><PERSON>() is deprecated due to security and usability issues. Please use the Buffer.alloc(), Buffer.allocUnsafe(), or Buffer.from() methods instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
Query execution error: Failed to connect to parkwizvms.database.windows.net:1433 - getaddrinfo ENOTFOUND parkwizvms.database.windows.net
SQL Server connection pool error: ConnectionError: Failed to connect to parkwizvms.database.windows.net:1433 - getaddrinfo ENOTFOUND parkwizvms.database.windows.net
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\mssql\lib\tedious\connection-pool.js:85:17
    at Connection.onConnect (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:849:9)
    at Object.onceWrapper (node:events:633:26)
    at Connection.emit (node:events:518:28)
    at Connection.emit (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:970:18)
    at Connection.socketError (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1353:12)
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1146:14
    at process.processTicksAndRejections (node:internal/process/task_queues:85:11) {
  code: 'ESOCKET',
  originalError: ConnectionError: Failed to connect to parkwizvms.database.windows.net:1433 - getaddrinfo ENOTFOUND parkwizvms.database.windows.net
      at Connection.socketError (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1353:28)
      at C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1146:14
      at process.processTicksAndRejections (node:internal/process/task_queues:85:11) {
    code: 'ESOCKET',
    [cause]: Error: getaddrinfo ENOTFOUND parkwizvms.database.windows.net
        at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
      errno: -3008,
      code: 'ENOTFOUND',
      syscall: 'getaddrinfo',
      hostname: 'parkwizvms.database.windows.net'
    }
  }
}
Auth middleware error: ConnectionError: Failed to connect to parkwizvms.database.windows.net:1433 - getaddrinfo ENOTFOUND parkwizvms.database.windows.net
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\mssql\lib\tedious\connection-pool.js:85:17
    at Connection.onConnect (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:849:9)
    at Object.onceWrapper (node:events:633:26)
    at Connection.emit (node:events:518:28)
    at Connection.emit (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:970:18)
    at Connection.socketError (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1353:12)
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1146:14
    at process.processTicksAndRejections (node:internal/process/task_queues:85:11) {
  code: 'ESOCKET',
  originalError: ConnectionError: Failed to connect to parkwizvms.database.windows.net:1433 - getaddrinfo ENOTFOUND parkwizvms.database.windows.net
      at Connection.socketError (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1353:28)
      at C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:1146:14
      at process.processTicksAndRejections (node:internal/process/task_queues:85:11) {
    code: 'ESOCKET',
    [cause]: Error: getaddrinfo ENOTFOUND parkwizvms.database.windows.net
        at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26) {
      errno: -3008,
      code: 'ENOTFOUND',
      syscall: 'getaddrinfo',
      hostname: 'parkwizvms.database.windows.net'
    }
  }
}
