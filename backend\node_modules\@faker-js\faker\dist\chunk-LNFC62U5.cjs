"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var a=["074## ######","075## ######","076## ######","077## ######","078## ######","079## ######"];var x={formats:a},t=x;var n=["ac.uk","biz","co","co.uk","com","cymru","gov.uk","info","london","ltd.uk","me.uk","name","nhs.uk","org.uk","plc.uk","sch.uk","scot","uk","wales"];var P={domain_suffix:n},i=P;var l=["###","##","#"];var d=["-under-","-over-","-le-","-upon-","-on-"];var s=["{{location.city_prefix}} {{person.last_name.generic}}{{location.city_suffix}}","{{location.city_prefix}} {{person.last_name.generic}}","{{person.last_name.generic}}{{location.city_suffix}}","{{person.last_name.generic}}{{location.city_infix}}{{person.last_name.generic}}"];var h=["Great","Little","St.","West","East","North","South","Upper","Lower","Old","Long","New","High","Nether","Castle","Upton","Newton"];var m=["ton","ham","ley","ington","ford","field","bury","don","ing","worth","well","ingham","wood","ridge","borough","stone","hill","thorpe","hampton","wick"," Green"," Park"," Hill"," Court"," Heath"," Bridge"," End"," Common"," Place"," Cross"," Gardens"];var u=["Avon","Bedfordshire","Berkshire","Borders","Buckinghamshire","Cambridgeshire","Central","Cheshire","Cleveland","Clwyd","Cornwall","County Antrim","County Armagh","County Down","County Fermanagh","County Londonderry","County Tyrone","Cumbria","Derbyshire","Devon","Dorset","Dumfries and Galloway","Durham","Dyfed","East Sussex","Essex","Fife","Gloucestershire","Grampian","Greater Manchester","Gwent","Gwynedd County","Hampshire","Herefordshire","Hertfordshire","Highlands and Islands","Humberside","Isle of Wight","Kent","Lancashire","Leicestershire","Lincolnshire","Lothian","Merseyside","Mid Glamorgan","Norfolk","North Yorkshire","Northamptonshire","Northumberland","Nottinghamshire","Oxfordshire","Powys","Rutland","Shropshire","Somerset","South Glamorgan","South Yorkshire","Staffordshire","Strathclyde","Suffolk","Surrey","Tayside","Tyne and Wear","Warwickshire","West Glamorgan","West Midlands","West Sussex","West Yorkshire","Wiltshire","Worcestershire"];var C=["??# #??","??## #??"];var f=["England","Northern Ireland","Scotland","Wales"];var c=["ENG","NIR","SCT","WLS"];var p=["Abbey Road","Albany Road","Albert Road","Albion Street","Alexandra Road","Alfred Street","Alma Street","Ash Close","Ash Grove","Ash Road","Aspen Close","Avenue Road","Back Lane","Baker Street","Balmoral Road","Barn Close","Barton Road","Bath Road","Bath Street","Beach Road","Bedford Road","Beech Close","Beech Drive","Beech Grove","Beech Road","Beechwood Avenue","Bell Lane","Belmont Road","Birch Avenue","Birch Close","Birch Grove","Birch Road","Blind Lane","Bluebell Close","Boundary Road","Bramble Close","Bramley Close","Broad Lane","Broad Street","Broadway","Brook Lane","Brook Road","Brook Street","Brookside","Buckingham Road","Cambridge Street","Castle Close","Castle Lane","Castle Road","Castle Street","Cavendish Road","Cedar Avenue","Cedar Close","Cedar Grove","Cedar Road","Cemetery Road","Central Avenue","Chapel Close","Chapel Hill","Chapel Road","Chapel Street","Charles Street","Cherry Close","Cherry Tree Close","Chester Road","Chestnut Close","Chestnut Drive","Chestnut Grove","Church Avenue","Church Close","Church Hill","Church Lane","Church Path","Church Road","Church View","Church Walk","Claremont Road","Clarence Road","Clarence Street","Clarendon Road","Clay Lane","Cliff Road","Clifton Road","Commercial Road","Commercial Street","Common Lane","Coronation Avenue","Coronation Road","Cow Lane","Crescent Road","Cromwell Road","Cross Lane","Cross Street","Crown Street","Dale Street","Dark Lane","Derby Road","Derwent Close","Devonshire Road","Douglas Road","Duke Street","East Avenue","East Road","Edward Street","Elm Close","Elm Grove","Elm Road","Fairfield Road","Farm Close","Ferry Road","Field Close","Field Lane","First Avenue","Fore Street","Forest Road","Fourth Avenue","Front Street","Garden Close","Garden Street","George Street","Gladstone Road","Glebe Close","Gloucester Road","Gordon Road","Gordon Street","Grange Avenue","Grange Close","Grange Road","Green Close","Green Lane","Green Street","Greenway","Grove Lane","Grove Road","Hall Lane","Hall Street","Hawthorn Avenue","Hawthorn Close","Hazel Close","Hazel Grove","Heath Road","Heather Close","Henry Street","Heron Close","High Road","High Street","Highfield Avenue","Highfield Close","Highfield Road","Hill Road","Hill Street","Hillside Avenue","Hillside Close","Hillside Road","Hillside","Holly Close","Honeysuckle Close","Howard Road","James Street","Jubilee Close","Juniper Close","Kent Road","Kestrel Close","King Street","King's Road","Kingfisher Close","Kingsway","Laburnum Grove","Lancaster Road","Lansdowne Road","Larch Close","Laurel Close","Lime Grove","Lincoln Road","Lodge Close","Lodge Lane","London Road","Long Lane","Low Road","Main Road","Main Street","Manor Close","Manor Drive","Manor Gardens","Manor Road","Manor Way","Maple Close","Maple Drive","Maple Road","Market Place","Market Square","Marlborough Road","Marsh Lane","Mary Street","Mayfield Road","Meadow Close","Meadow Drive","Meadow Lane","Meadow View","Meadow Way","Middle Street","Mill Close","Mill Lane","Mill Road","Mill Street","Milton Road","Milton Street","Moor Lane","Moss Lane","Mount Pleasant","Mount Street","Nelson Road","Nelson Street","New Lane","New Road","New Street","Newton Road","Nightingale Close","Norfolk Road","North Avenue","North Lane","North Road","Northfield Road","Oak Avenue","Oak Drive","Oak Lane","Oak Road","Oak Street","Oakfield Road","Oaklands","Old Lane","Old Military Road","Old Road","Orchard Drive","Orchard Lane","Orchard Road","Orchard Street","Oxford Road","Oxford Street","Park Avenue","Park Crescent","Park Drive","Park Lane","Park Place","Park Road","Park Street","Park View","Parkside","Pine Close","Pine Grove","Pinfold Lane","Poplar Avenue","Poplar Close","Poplar Road","Pound Lane","Princes Street","Princess Street","Priory Close","Priory Road","Prospect Place","Prospect Road","Quarry Lane","Quarry Road","Queen's Road","Railway Street","Rectory Close","Rectory Lane","Richmond Close","Richmond Road","Riverside","Roman Road","Roman Way","Rowan Close","Russell Street","Salisbury Road","Sandringham Road","Sandy Lane","School Close","School Lane","School Road","Second Avenue","Silver Street","Smith Street","Somerset Road","South Drive","South Road","South Street","South View","Spring Gardens","Springfield Close","Springfield Road","St Andrew's Road","St Andrews Close","St George's Road","St John's Road","St Mary's Close","St Mary's Road","Stanley Road","Stanley Street","Station Road","Station Street","Stoney Lane","Sycamore Avenue","Sycamore Close","Sycamore Drive","Talbot Road","Tennyson Road","The Avenue","The Beeches","The Causeway","The Chase","The Coppice","The Copse","The Crescent","The Croft","The Dell","The Drive","The Fairway","The Glebe","The Grange","The Green","The Grove","The Hawthorns","The Lane","The Laurels","The Limes","The Maltings","The Meadows","The Mews","The Mount","The Oaks","The Orchard","The Oval","The Paddock","The Paddocks","The Poplars","The Ridgeway","The Ridings","The Rise","The Sidings","The Spinney","The Square","The Willows","The Woodlands","Third Avenue","Tower Road","Trinity Road","Tudor Close","Union Street","Valley Road","Vicarage Close","Vicarage Lane","Vicarage Road","Victoria Place","Victoria Road","Victoria Street","Walnut Close","Warren Close","Warren Road","Water Lane","Water Street","Waterloo Road","Waterside","Watery Lane","Waverley Road","Well Lane","Wellington Road","Wellington Street","West End","West Lane","West Street","West View","Western Avenue","Western Road","Westfield Road","Westgate","William Street","Willow Close","Willow Drive","Willow Grove","Willow Road","Windermere Road","Windmill Close","Windmill Lane","Windsor Avenue","Windsor Close","Windsor Drive","Wood Lane","Wood Street","Woodland Close","Woodland Road","Woodlands Avenue","Woodlands Close","Woodlands Road","Woodlands","Woodside Road","Woodside","Wren Close","Yew Tree Close","York Road","York Street"];var R=["{{person.firstName}} {{location.street_suffix}}","{{person.lastName}} {{location.street_suffix}}","{{location.street_name}}"];var S=["Road","Close","Street","Lane","Avenue","Drive","Way","Place","Court","Gardens","Crescent","Grove","Terrace","Hill","View","Walk","Park","Mews","Rise","Green","Square","Croft","Bank","Row","Meadow","Gate","End","Drove","Mead","Field","Chase","Mount","Meadows","Orchard","Fields","Yard","Garth","Fold","Wynd","Parade","Vale","Brae","Grange","Approach","Wood","Paddock","Brow","Lea","Path","Side","Heights","Copse","Corner","Ridge","Glade"];var D={building_number:l,city_infix:d,city_pattern:s,city_prefix:h,city_suffix:m,county:u,postcode:C,state:f,state_abbr:c,street_name:p,street_pattern:R,street_suffix:S},y=D;var A={title:"English (Great Britain)",code:"en_GB",country:"GB",language:"en",endonym:"English (Great Britain)",dir:"ltr",script:"Latn"},g=A;var L={generic:[{value:"{{person.last_name.generic}}",weight:9},{value:"{{person.last_name.generic}}-{{person.last_name.generic}}",weight:1}]};var w=[{value:"{{person.firstName}} {{person.lastName}}",weight:7},{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1}];var H={last_name_pattern:L,name:w},v=H;var W=["01#### #####","01### ######","01#1 ### ####","011# ### ####","02# #### ####","03## ### ####","055 #### ####","056 #### ####","0800 ### ####","08## ### ####","09## ### ####","016977 ####","01### #####","0500 ######","0800 ######"];var k=["+441#########","+441#1#######","+4411########","+442#########","+443#########","+4455########","+4456########","+44800#######","+448#########","+449#########","+4416977####","+441########","+44500######","+44800######"];var G=["01### ######","01#1 ### ####","011# ### ####","02# #### ####","03## ### ####","055 #### ####","056 #### ####","0800 ### ####","08## ### ####","09## ### ####","016977 ####","01### #####","500######","0800 ######"];var _={human:W,international:k,national:G},B=_;var b={format:B},M=b;var N={cell_phone:t,internet:i,location:y,metadata:g,person:v,phone_number:M},T= exports.a =N;var Ve=new (0, _chunkZKNYQOPPcjs.n)({locale:[T,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = T; exports.b = Ve;
