🔍 Permission Management Routes loaded
🚀 Initializing Redis and services...
Server running on port \\.\pipe\dc15590d-cf6b-4c18-a023-ac80c2979e92
✅ Redis client connected successfully
✅ Redis client ready for operations
🚀 Redis connection established successfully
✅ RedisService initialized successfully
✅ RealtimeService initialized successfully
✅ All services initialized successfully
Database connected: ParkwizOps
Server: parkwizvms.database.windows.net, User: hparkwiz
🔍 Database Session Info:
  - Isolation Level: ReadCommitted
  - Session ID: 71
  - Login Name: hparkwiz
  - Database: ParkwizOps
  - Server: parkwizvms
  - Server Time: Fri Jul 04 2025 20:47:55 GMT+0530 (India Standard Time)
  - Server Time with Offset: Fri Jul 04 2025 20:47:55 GMT+0530 (India Standard Time)
🔍 Getting hourly entry/exit data for user 1 (SuperAdmin) with filters: {
  dateRange: 'today',
  companyId: undefined,
  plazaId: undefined,
  laneId: undefined
}
🔍 Executing hourly entry/exit query...
✅ Successfully retrieved hourly entry/exit data: 24 hours
🔍 Getting hourly entry/exit data for user 1 (SuperAdmin) with filters: {
  dateRange: 'today',
  companyId: undefined,
  plazaId: undefined,
  laneId: undefined
}
📦 Returning cached hourly entry/exit data
🔍 Getting hourly entry/exit data for user 1 (SuperAdmin) with filters: {
  dateRange: 'today',
  companyId: undefined,
  plazaId: undefined,
  laneId: undefined
}
🔍 Executing hourly entry/exit query...
✅ Successfully retrieved hourly entry/exit data: 24 hours
