"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var i=["Ltd","Plc","Venture"];var c={legal_entity_type:i},n=c;var u=["com","com.ng","ng","org.ng"];var B={domain_suffix:u},r=B;var t=["<PERSON>bal<PERSON>","Abalakiri","Bekirikiri","Bela","Belatiwa","Bokodo","Ibinta","Ibiono Ewura","<PERSON><PERSON> Yashe","Fedare","Fobir","Fobro","Gabgell","Gamajigo","Gana","Gana Daji","Gidan Hardo","Abagbo","<PERSON>balabi","<PERSON><PERSON>n<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON> Ijesha","<PERSON>le-<PERSON><PERSON>","<PERSON><PERSON>","Ad<PERSON>","Adekanbi","<PERSON><PERSON>","Ado","Ado Odo","Afami","Afanji","Afowo Doforo","Afowowa","Agada","Agaja","Agala","Agani","Aganni","Agaw Awusa","Agaw Shasha","Agbaku","Agbara","Agbede","Agbedi","Agbejedo","Agbele","Agbelekale","Agboju","Agboku","Agbon","Agbonyedo","Agbowa","Agboyi","Age Mowo","Agege","Agelete","Agerige","Agidi","Agidingbi","Ago Egun","Ago Hausa","Ago Sasa","Agomu","Agonrin","Agonu","Aguda","Agun","Agunfoye","Agura","Ahanfe","Ahovo","Ahun","Aiyede","Aiyeteju","Aiyetoro","Aiyetoto-Asogun","Aja","Ajagambari","Ajara","Ajara Agamaden","Ajebandele","Ajebaw","Ajebo","Ajegbenwa","Ajegunle","Ajelanwa","Ajerogun","Ajibade","Ajibawo","Ajibo","Ajido","Ajise","Ajumo","Akando","Akangba","Akarakumo","Akawdo","Akawkaw","Akere","Akessan","Akete","Akinbo","Akinlade","Akinogun","Akinyele","Akio","Akobale","Akodo","Akoko","Akore","Akowonjo","Alagba","Alagbede","Alagbon","Alago","Alagogo","Alaguntan","Alaiyabiagba","Alapako","Alaparu","Alaparun","Alapoti","Alaru","Alasia","Ale","Aliayabiagba","Alimosho","Alimoso","Amuwo","Anagoji","Anagunji","Angorin","Animashawun","Animshaun","Apa","Apamu","Apapa","Apapa Eleko","Apese","Appa","Aqani","Aradagun","Arapagi Oloko","Arapagi-Awlawkaw","Arapagi-Ejetu","Araromi","Araromi Orita","Araromi Tawpe","Araromi Tope","Arida","Arigo","Ashon","Asipa","Ason","Asore","Asunora","Atan","Atapa","Awado","Awaiye","Awaye","Awdan Ilaro","Awdan Iyasi","Awfin","Awgawgawraw","Awgawmbaw","Awja-Ikoradu","Awjaw","Awoyaya","Awreta","Awteyi","Awwaw","Ayekoshe","Ayinla","Ayobo","Babalawo","Badagri","Badagry","Badawre","Badore","Baiyeku","Balagbe","Balogun","Bamgbose","Bamgboshe","Bandu","Banimgbe","Banimgbo","Bariga","Baruwa","Bassa","Beshi","Bodashe","Bode Ase","Bolorunpelu","Cardoso","Coker","Coker Market","Cokers Market","Dankaka","Doforo","Dosa","Dosemo","Ebute Ikorodu","Ebute Leki","Ebute Lekki","Ebute-Egga","Ebute-Metta","Efiran","Egan","Egba","Egbe","Eggan","Egudu Bale","Egun","Eiyekose","Ejigbo","Ejinrin","Ejirin","Eko","Ekoro","Ekundayo","Elachi","Elemoki","Eleputu","Elere","Elesin","Eluju","Emu","Epe","Epeh","Era","Ere","Eregun","Erekiti","Erukan","Eruku","Erunkan","Etegbin","Euni","Ewekora","Ewekoro","Ewu","Ewu Ebi","Falomo","Fatade","Fatedo","Fonji","Ganme","Ganyingbo","Gbagidan","Gbaj","Gbaji","Gbanko","Gberegbe","Gberigbe","Gbesse","Gbeta","Gbodo","Gbogbo","Gbogije","Gbokuta","George","Ginti","Hausa","Henume","Hundo","Iba","Ibadan","Ibasa","Ibasha","Ibatefin","Ibawe Kekere","Ibawe Tukuru","Ibbojo","Ibefon","Ibefum","Ibefun","Ibeju","Ibereko","Iberekodo","Ibese","Ibeshe","Ibeshi","Ibiku","Ibode","Ibogun Adina","Ibogun Akiode","Ibogun Sowunmi","Iboju","Ibon","Ibopa","Ida","Idain Isaga","Idain-Ishaga","Idamo","Idanfuru","Idashaw","Idaso","Idata","Iddo","Ide","Idele","Ideno","Idi Agbon Agana","Idi Iroko","Idi Ofe","Idi Oro","Idi-Off","Idimarun","Idimi","Idimu","Idiori","Idiroko","Idobarun","Idoforo","Idolanja","Idole","Idolehin","Idolorisha","Idolosa","Idomu","Idopetu","Idosa","Idosemo","Idowu","Ifako","Igami","Igando","Igando Awlawja","Iganmi","Iganmu","Iganmu Siding","Igbalu","Igbe","Igbe Ewoliwo","Igbede","Igbegodo","Igbekun","Igbele","Igbessa","Igbin","Igbin Oloya","Igbo","Igbo Ejo","Igbo Fipe","Igbobi","Igbodu","Igboefon","Igbogbele","Igbogila","Igbogun","Igbogun-Sowunmi","Igboje","Igbokushun","Igbolobi","Igbologun","Igboloye","Igbopa","Igborosun","Igbosa","Igbosere","Igboshere","Igodonu","Igoro","Igu","Igude","Igunnu Akabo","Ijagemo","Ijaiye","Ijako","Ijako Orile","Ijanikin","Ijawmi","Ijawmu","Ijawtun","Ijayie","Ijede","Ijegun","Ijero","Ijesa-Tedo","Ijofin","Ijomi","Ijomu","Ijora","Ijora Village","Ijotan","Iju","Iju Junction","Iju Water Works","Ikare","Ikate","Ikawga","Ikawga-Zebe","Ikawlaji","Ikawta","Ikeja","Ikoga","Ikoga Ile","Ikogbo","Ikolaja","Ikorodu","Ikosi","Ikotan","Ikotun","Ikoyi","Ikuata","Ilade","Ilado","Ilado Ogunu","Ilagbo","Ilasa","Ilashe","Ilemere","Ilepa","Ileppaw","Ilera","Ilikiti","Ilo","Ilogbo","Ilogbo Elegba","Iloro","Ilugboro","Ilumawfin","Ilumofin","Imare","Imawte","Imeke","Imeri","Imeseju","Imore","Imoru","Imota","Inogbe","Inupa","Inupa Kekere","Ipaja","Ipakan","Ipanmi","Ipatira","Ipeshu","Ipesu","Ipewu","Ipokia","Iponri","Ipota","Iragbo","Iragon","Iranla","Irede","Irewe","Iru","Isagatedo","Isagbo","Isagbo Ere","Isagira","Isaku","Isalu","Isashi","Isasi","Isawo","Ise","Iseku","Isekun","Iseri-Osun","Ishaga","Ishagbo","Ishagira","Ishasi","Ishawo","Isheri-Olofin","Ishersi","Ishola","Isiu","Isiwu","Isola","Isolo","Isunba","Ita Egbe","Ita Onimosa","Itagbo","Itamaga","Itawga","Itawikin","Itawmu","Iteku","Itele","Itere","Itire","Itirin","Ito Omu","Itoga","Itohun","Itoikin","Itokin","Itomu","Itori","Iwaya","Iwerekun","Iworo","Iwuku","Iyafin","Iyagbe","Iyasi","Iyesi","Jaguna","Janikin","Jibowu","Jinadu","Kadara","Kafara","Kajola","Kajola Iboro","Kandoro","Kese"];var m=["{{location.city_name}}"];var l=["#####","####"];var I=["FCT","ABIA","ADAMAWA","Akwa Ibom","Anambra","Bauchi","Bayelsa","Benue","Bornu","Cross River","Delta","Ebonyi","Edo","Ekiti","Enugu","Gombe","Imo","Jigawa","Kaduna","Kano","Katsina","Kebbi","Kogi","Kwara","Lagos","Nasarawa","Niger","Ogun","Ondo","Osun","Oyo","Plateau","Rivers","Sokoto","Taraba","Yobe","Zamfara"];var b=["{{person.firstName}} {{location.street_suffix}}","{{person.lastName}} {{location.street_suffix}}"];var G={city_name:t,city_pattern:m,postcode:l,state:I,street_pattern:b},A=G;var C={title:"English (Nigeria)",code:"en_NG",country:"NG",language:"en",endonym:"English (Nigeria)",dir:"ltr",script:"Latn"},g=C;var d={generic:["Abimbola","Abisola","Abisoye","Adaugo","Adeboye","Adedayo","Adegoke","Akande","Akanni","Akunna","Alade","Aminat","Aminu","Augustina","Ayebatari","Ayinde","Azubuike","Banji","Bankole","Buchi","Bukola","Cherechi","Chiamaka","Chimamanda","Chinedu","Chinyere","Chisom","Chizoba","Chukwu","Damilare","Damilola","Danjuma","Ebiere","Ebiowei","Efe","Emeka","Emmanuel","Esse","Fatima","Funmilade","Funmilayo","Gbeminiyi","Gbemisola","Habiba","Ifeanyichukwu","Ifeoma","Ifunanya","Ikenna","Ikhidie","Ireti","Isioma","Jadesola","Johnson","Jolayemi","Kayode","Kemi","Kubra","Kubura","Lola","Lolade","Makinwa","Mohammed","Musa","Muyiwa","Nnamdi","Obioma","Olaide","Olufunmi","Olumide","Oluwunmi","Omawunmi","Omolara","Onome","Onoriode","Rasheedah","Remilekun","Rotimi","Sekinat","Shade","Shalewa","Simisola","Sname","Sumayyah","Tari","Temitope","Titi","Titilayo","Titilope","Tobiloba","Toke","Toluwani","Tomiloba","Tope","Uzodimma","Wale","Yakubu","Yusuf","Zainab"],female:["Adaugo","Akunna","Aminat","Aminu","Augustina","Ayebatari","Cherechi","Chiamaka","Chimamanda","Chinyere","Chizoba","Ebiere","Efe","Fatima","Ifeoma","Ifunanya","Isioma","Jolayemi","Lola","Obioma","Omawunmi","Omolara","Onome","Rasheedah","Sekinat","Simisola","Sumayyah","Titi","Titilayo","Toluwani","Zainab"],male:["Abimbola","Abisola","Abisoye","Adeboye","Adedayo","Adegoke","Akande","Akanni","Alade","Ayinde","Azubuike","Banji","Bankole","Buchi","Bukola","Chinedu","Chisom","Chukwu","Damilare","Damilola","Danjuma","Ebiowei","Emeka","Emmanuel","Esse","Funmilade","Funmilayo","Gbeminiyi","Gbemisola","Habiba","Ifeanyichukwu","Ikenna","Ikhidie","Ireti","Jadesola","Johnson","Kayode","Kemi","Kubra","Kubura","Lolade","Makinwa","Mohammed","Musa","Muyiwa","Nnamdi","Olaide","Olufunmi","Olumide","Oluwunmi","Onoriode","Remilekun","Rotimi","Shade","Shalewa","Sname","Tari","Temitope","Titilope","Tobiloba","Toke","Tomiloba","Tope","Uzodimma","Wale","Yakubu","Yusuf"]};var s={generic:["Abiodun","Abiola","Abodunrin","Abosede","Adaobi","Adebayo","Adegboye","Adegoke","Ademayowa","Ademola","Adeniyan","Adeoluwa","Aderinsola","Aderonke","Adesina","Adewale","Adewunmi","Adewura","Adeyemo","Afolabi","Afunku","Agboola","Agnes","Aigbiniode","Ajakaiye","Ajose-adeogun","Akeem-omosanya","Akerele","Akintade","Aligbe","Amaechi","Aminat","Aremu","Atanda","Ayisat","Ayobami","Ayomide","Babalola","Babatunde","Balogun","Bamisebi","Bello","Busari","Chibike","Chibuike","Chidinma","Chidozie","Christian","Clare","David","Ebubechukwu","Egbochukwu","Ehigiator","Ekwueme","Elebiyo","Elizabeth","Emmanuel","Esther","Funmilayo","Gbadamosi","Gbogboade","Grace","Habeeb","Hanifat","Isaac","Ismail","Isokun","Israel","Iyalla","Jamiu","Jimoh","Joshua","Justina","Katherine","Kayode","Kimberly","Ladega","Latifat","Lawal","Leonard","Makuachukwu","Maryam","Maryjane","Mayowa","Miracle","Mobolaji","Mogbadunade","Motalo","Muinat","Mukaram","Mustapha","Mutiat","Ndukwu","Ngozi","Nojeem","Nwachukwu","Nwogu","Nwuzor","Obiageli","Obianuju","Odunayo","Ogunbanwo","Ogunwande","Okonkwo","Okunola","Oladeji","Oladimeji","Olaoluwa","Olasunkanmi","Olasunkanmi-fasayo","Olawale","Olubukola","Olubunmi","Olufeyikemi","Olumide","Olutola","Oluwakemi","Oluwanisola","Oluwaseun","Oluwaseyi","Oluwashina","Oluwatosin","Omobolaji","Omobolanle","Omolara","Omowale","Onohinosen","Onose","Onyinyechukwu","Opeyemi","Osuagwu","Oyebola","Oyelude","Oyinkansola","Peter","Sabdat","Saheed","Salami","Samuel","Sanusi","Sarah","Segunmaru","Sekinat","Sulaimon","Sylvester","Taiwo","Tamunoemi","Tella","Temitope","Tolulope","Uchechi","Wasiu","Wilcox","Wuraola","Yaqub","Yussuf"]};var k={generic:[{value:"{{person.last_name.generic}}",weight:95},{value:"{{person.last_name.generic}}-{{person.last_name.generic}}",weight:5}]};var w=[{value:"{{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.lastName}} {{person.firstName}}",weight:1}];var D={first_name:d,last_name:s,last_name_pattern:k,name:w},p=D;var f=["0803 ### ####","0703 ### ####","234809 ### ####","+234 802 ### ####","0805### ####"];var y=["+234803#######","+234703#######","+234809#######","+234802#######","+234805#######"];var h=["0803 ### ####","0703 ### ####","0809 ### ####","0802 ### ####","0805 ### ####"];var T={human:f,international:y,national:h},j=T;var S={format:j},E=S;var x={company:n,internet:r,location:A,metadata:g,person:p,phone_number:E},O= exports.a =x;var Ba=new (0, _chunkZKNYQOPPcjs.n)({locale:[O,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = O; exports.b = Ba;
