<html>
<head>
    <title>iisnode logs</title>
    <style type="text/css">
        body
        {
            font-family: "Trebuchet MS" , Arial, Helvetica, sans-serif;
        }
        table
        {
            border-collapse: collapse;
        }
        td, th
        {
            border: 1px solid lightgray;
            padding: 3px 7px 2px 7px;
        }
        th
        {
            text-align: left;
            padding-top: 5px;
            padding-bottom: 4px;
            background-color: Gray;
            color: #ffffff;
        }
        td.stderr
        {
            color: Red;
        }
    </style>
</head>
<body>
    <table id="logFilesTable">
        <tr>
            <th>
                Computer name
            </th>
            <th>
                PID
            </th>
            <th>
                Type
            </th>
            <th>
                Created
            </th>
            <th>
                Link
            </th>
        </tr>
    </table>
    <p id="lastUpdated"></p>
    <script type="text/javascript">

        // this is replaced with actual data at runtime by code in interceptor.js
        var logFiles = [{"file":"PARKWIZ-IT-14932-stderr-1751372377562.txt","computername":"PARKWIZ-IT","pid":14932,"type":"stderr","created":1751372377562},{"file":"PARKWIZ-IT-14932-stdout-1751372377557.txt","computername":"PARKWIZ-IT","pid":14932,"type":"stdout","created":1751372377557},{"file":"PARKWIZ-IT-15168-stderr-1751371961274.txt","computername":"PARKWIZ-IT","pid":15168,"type":"stderr","created":1751371961274},{"file":"PARKWIZ-IT-15168-stderr-1751394454019.txt","computername":"PARKWIZ-IT","pid":15168,"type":"stderr","created":1751394454019},{"file":"PARKWIZ-IT-15168-stdout-1751394454013.txt","computername":"PARKWIZ-IT","pid":15168,"type":"stdout","created":1751394454013},{"file":"PARKWIZ-IT-1788-stderr-1751552406264.txt","computername":"PARKWIZ-IT","pid":1788,"type":"stderr","created":1751552406264},{"file":"PARKWIZ-IT-1788-stdout-1751552406259.txt","computername":"PARKWIZ-IT","pid":1788,"type":"stdout","created":1751552406259},{"file":"PARKWIZ-IT-21424-stderr-1751641890724.txt","computername":"PARKWIZ-IT","pid":21424,"type":"stderr","created":1751641890724},{"file":"PARKWIZ-IT-21424-stdout-1751641890722.txt","computername":"PARKWIZ-IT","pid":21424,"type":"stdout","created":1751641890722},{"file":"PARKWIZ-IT-2336-stderr-1751373248336.txt","computername":"PARKWIZ-IT","pid":2336,"type":"stderr","created":1751373248336},{"file":"PARKWIZ-IT-2336-stdout-1751373248331.txt","computername":"PARKWIZ-IT","pid":2336,"type":"stdout","created":1751373248331},{"file":"PARKWIZ-IT-23396-stderr-1751262513187.txt","computername":"PARKWIZ-IT","pid":23396,"type":"stderr","created":1751262513187},{"file":"PARKWIZ-IT-23396-stdout-1751262513186.txt","computername":"PARKWIZ-IT","pid":23396,"type":"stdout","created":1751262513186},{"file":"PARKWIZ-IT-27700-stderr-1751641857214.txt","computername":"PARKWIZ-IT","pid":27700,"type":"stderr","created":1751641857214},{"file":"PARKWIZ-IT-27700-stdout-1751641857209.txt","computername":"PARKWIZ-IT","pid":27700,"type":"stdout","created":1751641857209},{"file":"PARKWIZ-IT-28096-stderr-1751463834507.txt","computername":"PARKWIZ-IT","pid":28096,"type":"stderr","created":1751463834507},{"file":"PARKWIZ-IT-28096-stdout-1751463834488.txt","computername":"PARKWIZ-IT","pid":28096,"type":"stdout","created":1751463834488},{"file":"PARKWIZ-IT-28600-stderr-1751642273816.txt","computername":"PARKWIZ-IT","pid":28600,"type":"stderr","created":1751642273816},{"file":"PARKWIZ-IT-28600-stdout-1751642273812.txt","computername":"PARKWIZ-IT","pid":28600,"type":"stdout","created":1751642273812},{"file":"PARKWIZ-IT-30732-stderr-1751571596652.txt","computername":"PARKWIZ-IT","pid":30732,"type":"stderr","created":1751571596652},{"file":"PARKWIZ-IT-30732-stdout-1751571596639.txt","computername":"PARKWIZ-IT","pid":30732,"type":"stdout","created":1751571596639},{"file":"PARKWIZ-IT-7036-stderr-1751261912134.txt","computername":"PARKWIZ-IT","pid":7036,"type":"stderr","created":1751261912134}];
        var lastUpdated = 1751642274767;
        var date = new Date();

        date.setTime(lastUpdated);
        document.getElementById('lastUpdated').innerHTML = 'Index was last updated ' + date;

        logFiles.sort(function (a, b) {
            return b.created - a.created;
        });

        var logFilesTable = document.getElementById("logFilesTable");
        for (var i = 0; i < logFiles.length; i++) {
            var logFile = logFiles[i];
            date.setTime(logFile.created);
            var row = logFilesTable.insertRow(-1);
            var computerNameCell = row.insertCell(0);
            var pidCell = row.insertCell(1);
            var typeCell = row.insertCell(2);
            var dateCell = row.insertCell(3);
            var logCell = row.insertCell(4);
            computerNameCell.innerHTML = logFile.computername;
            pidCell.innerHTML = logFile.pid.toString();
            typeCell.innerHTML = logFile.type;
            typeCell.setAttribute('class', logFile.type);
            dateCell.innerHTML = date.toString();
            logCell.innerHTML = '<a href="' + logFile.file + '">log</a>';
        };

    </script>
</body>
</html>
