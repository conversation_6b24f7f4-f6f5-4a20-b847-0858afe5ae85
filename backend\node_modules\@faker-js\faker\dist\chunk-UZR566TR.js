import{a as r}from"./chunk-BKUYYLI4.js";import{a as o}from"./chunk-KERBADJJ.js";import{n as t,o as e}from"./chunk-PC2QB7VM.js";var i=["621 ### ###","661 ### ###","671 ### ###","691 ### ###","+352 621 ### ###","+352 661 ### ###","+352 671 ### ###","+352 691 ### ###"];var b={formats:i},n=b;var m=["lu"];var P={domain_suffix:m},a=P;var f=["Diekirch","Differdange","Dudelange","Echternach","Esch-sur-Alzette","Ettelbruck","Grevenmacher","Luxembourg","Remich","Rumelange","Vianden","Wiltz"];var p=["{{location.city_name}}"];var l=["####"];var u=["Capellen","Clervaux","Diekirch","E<PERSON>ernach","Esch-sur-Alzette","Grevenmacher","Luxembourg","Mersch","Redange","Remich","Vianden","Wiltz"];var k={city_name:f,city_pattern:p,postcode:l,state:u},c=k;var E={title:"French (Luxembourg)",code:"fr_LU",country:"LU",language:"fr",endonym:"Fran\xE7ais (Luxembourg)",dir:"ltr",script:"Latn"},d=E;var s={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var F={last_name_pattern:s},x=F;var h=["######","########","+352 ######","+352 ########"];var D=["+352######","+352########"];var y=["## ## ##","## ## ## ##"];var v={human:h,international:D,national:y},_=v;var z={format:_},g=z;var C={cell_phone:n,internet:a,location:c,metadata:d,person:x,phone_number:g},L=C;var ht=new t({locale:[L,r,o,e]});export{L as a,ht as b};
