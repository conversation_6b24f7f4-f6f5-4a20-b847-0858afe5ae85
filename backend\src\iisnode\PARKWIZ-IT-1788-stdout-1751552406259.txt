Environment variables loaded:
DB_SERVER: parkwizvms.database.windows.net
DB_NAME: ParkwizOps
DB_USER: hparkwiz
DB_PORT: 1433
🚀 Initializing Redis and services...
Server running on port \\.\pipe\3214fa72-30d9-4168-9171-5eaa1b560bf6
✅ Redis client connected successfully
✅ Redis client ready for operations
🚀 Redis connection established successfully
✅ RedisService initialized successfully
✅ RealtimeService initialized successfully
✅ All services initialized successfully
Database connected: ParkwizOps
Database connected: ParkwizOps
✅ Dashboard summary served from Redis cache
✅ Revenue by payment method served from Redis cache
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
Retrying query, 3 attempts left
