"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var r=["biz","com","info","name","net","org","us"];var y={domain_suffix:r},i=y;var a=["{{location.city_prefix}} {{person.firstName}}{{location.city_suffix}}","{{location.city_prefix}} {{person.firstName}}","{{person.firstName}}{{location.city_suffix}}","{{person.last_name.generic}}{{location.city_suffix}}"];var o=["Adams County","Calhoun County","Carroll County","Clark County","Clay County","Crawford County","Douglas County","Fayette County","Franklin County","Grant County","Greene County","Hamilton County","Hancock County","Henry County","Jackson County","Jefferson County","Johnson County","Lake County","Lawrence County","Lee County","Lincoln County","Logan County","Madison County","Marion County","Marshall County","Monroe County","Montgomery County","Morgan County","Perry County","Pike County","Polk County","Scott County","Union County","Warren County","Washington County","Wayne County"];var S={AK:'{{number.int({"min": 99501,"max": 99950})}}',AL:'{{number.int({"min": 35004,"max": 36925})}}',AR:['{{number.int({"min": 71601,"max": 72642})}}','{{number.int({"min": 72644,"max": 72959})}}'],AZ:'{{number.int({"min": 85001,"max": 86556})}}',CA:'{{number.int({"min": 90001,"max": 96162})}}',CO:'{{number.int({"min": 80001,"max": 81658})}}',CT:'0{{number.int({"min": 6001,"max": 6389})}}',DC:'{{number.int({"min": 20001,"max": 20039})}}',DE:'{{number.int({"min": 19701,"max": 19980})}}',FL:['{{number.int({"min": 32003,"max": 32099})}}','{{number.int({"min": 32102,"max": 32198})}}','{{number.int({"min": 32201,"max": 32290})}}','{{number.int({"min": 32301,"max": 32399})}}','{{number.int({"min": 32401,"max": 32466})}}','{{number.int({"min": 32501,"max": 32592})}}','{{number.int({"min": 32601,"max": 32697})}}','{{number.int({"min": 32701,"max": 32799})}}','{{number.int({"min": 32801,"max": 32899})}}','{{number.int({"min": 32901,"max": 32978})}}','{{number.int({"min": 33001,"max": 33097})}}','{{number.int({"min": 33101,"max": 33199})}}','{{number.int({"min": 33206,"max": 33299})}}','{{number.int({"min": 33301,"max": 33394})}}','{{number.int({"min": 33401,"max": 33499})}}','{{number.int({"min": 33503,"max": 33598})}}','{{number.int({"min": 33601,"max": 33694})}}','{{number.int({"min": 33701,"max": 33786})}}','{{number.int({"min": 33801,"max": 33898})}}','{{number.int({"min": 33900,"max": 33994})}}','{{number.int({"min": 34101,"max": 34146})}}','{{number.int({"min": 34201,"max": 34295})}}','{{number.int({"min": 34420,"max": 34498})}}','{{number.int({"min": 34601,"max": 34698})}}','{{number.int({"min": 34705,"max": 34797})}}','{{number.int({"min": 34945,"max": 34997})}}'],GA:'{{number.int({"min": 30001,"max": 31999})}}',HI:['{{number.int({"min": 96701,"max": 96798})}}','{{number.int({"min": 96801,"max": 96898})}}'],IA:'{{number.int({"min": 50001,"max": 52809})}}',ID:['{{number.int({"min": 83201,"max": 83406})}}','{{number.int({"min": 83415,"max": 83876})}}'],IL:'{{number.int({"min": 60001,"max": 62999})}}',IN:'{{number.int({"min": 46001,"max": 47997})}}',KS:'{{number.int({"min": 66002,"max": 67954})}}',KY:'{{number.int({"min": 40003,"max": 42788})}}',LA:'{{number.int({"min": 70001,"max": 71232})}}',MA:'0{{number.int({"min": 1001,"max": 2791})}}',MD:'{{number.int({"min": 20899,"max": 20908})}}',ME:'0{{number.int({"min": 3901,"max": 4992})}}',MI:'{{number.int({"min": 48001,"max": 49971})}}',MN:'{{number.int({"min": 55001,"max": 56763})}}',MO:'{{number.int({"min": 63001,"max": 65899})}}',MS:'{{number.int({"min": 38601,"max": 39776})}}',MT:'{{number.int({"min": 59001,"max": 59937})}}',NC:'{{number.int({"min": 27006,"max": 28909})}}',ND:'{{number.int({"min": 58001,"max": 58856})}}',NE:'{{number.int({"min": 68001,"max": 68118})}}',NH:'0{{number.int({"min": 3031,"max": 3897})}}',NJ:'0{{number.int({"min": 7001,"max": 8989})}}',NM:'{{number.int({"min": 87001,"max": 88441})}}',NV:'{{number.int({"min": 88901,"max": 89883})}}',NY:'0{{number.int({"min": 6390,"max": 6390})}}',OH:'{{number.int({"min": 43001,"max": 45999})}}',OK:'{{number.int({"min": 73001,"max": 73199})}}',OR:'{{number.int({"min": 97001,"max": 97920})}}',PA:'{{number.int({"min": 15001,"max": 19640})}}',PR:['00{{number.int({"min": 601,"max": 799})}}','00{{number.int({"min": 901,"max": 988})}}'],RI:'0{{number.int({"min": 2801,"max": 2940})}}',SC:'{{number.int({"min": 29001,"max": 29948})}}',SD:'{{number.int({"min": 57001,"max": 57799})}}',TN:'{{number.int({"min": 37010,"max": 38589})}}',TX:'{{number.int({"min": 75503,"max": 79999})}}',UT:'{{number.int({"min": 84001,"max": 84784})}}',VA:'{{number.int({"min": 22201,"max": 24599})}}',VT:'0{{number.int({"min": 5001,"max": 5495})}}',WA:'{{number.int({"min": 98001,"max": 99403})}}',WI:'{{number.int({"min": 53001,"max": 54990})}}',WV:'{{number.int({"min": 24701,"max": 26886})}}',WY:'{{number.int({"min": 82001,"max": 83128})}}'};var m=["10th Street","11th Street","12th Street","13th Street","14th Street","15th Street","16th Street","1st Avenue","1st Street","2nd Avenue","2nd Street","3rd Avenue","3rd Street","4th Avenue","4th Street","5th Avenue","5th Street","6th Avenue","6th Street","7th Avenue","7th Street","8th Avenue","8th Street","9th Street","A Street","Adams Avenue","Adams Street","Airport Road","Ash Street","Atlantic Avenue","Bay Street","Bridge Road","Bridge Street","Broad Street","Broadway","Broadway Avenue","Broadway Street","Canal Street","Cedar Street","Cemetery Road","Center Avenue","Center Road","Center Street","Central Avenue","Central Street","Charles Street","Cherry Street","Chestnut Street","Church Street","Clark Street","Cleveland Street","Clinton Street","College Avenue","College Street","Columbia Avenue","Commerce Street","Commercial Street","County Line Road","County Road","Court Street","Cross Street","Cumberland Street","Davis Street","Depot Street","Division Street","E 10th Street","E 11th Street","E 12th Street","E 14th Street","E 1st Street","E 2nd Street","E 3rd Street","E 4th Avenue","E 4th Street","E 5th Street","E 6th Avenue","E 6th Street","E 7th Street","E 8th Street","E 9th Street","E Bridge Street","E Broad Street","E Broadway","E Broadway Street","E Cedar Street","E Center Street","E Central Avenue","E Church Street","E Elm Street","E Franklin Street","E Front Street","E Grand Avenue","E High Street","E Jackson Street","E Jefferson Street","E Main","E Main Street","E Maple Street","E Market Street","E North Street","E Oak Street","E Park Avenue","E Pine Street","E River Road","E South Street","E State Street","E Union Street","E Walnut Street","E Washington Avenue","E Washington Street","E Water Street","East Avenue","East Street","Elm Street","Euclid Avenue","Ferry Road","First Street","Forest Avenue","Franklin Avenue","Franklin Road","Franklin Street","Front Street","Frontage Road","Grand Avenue","Grant Street","Green Street","Greenville Road","Greenwood Road","Grove Street","Harrison Avenue","Harrison Street","Hickory Street","High Street","Highland Avenue","Hill Street","Howard Street","Jackson Avenue","Jackson Street","Jefferson Avenue","Jefferson Street","Johnson Street","King Street","Kings Highway","Lafayette Street","Lake Avenue","Lake Drive","Lake Road","Lake Street","Lawrence Street","Lee Street","Liberty Street","Lincoln Avenue","Lincoln Highway","Lincoln Road","Lincoln Street","Locust Street","Madison Avenue","Madison Street","Main","Main Avenue","Main Road","Main Street","Main Street E","Main Street N","Main Street S","Main Street W","Manchester Road","Maple Avenue","Maple Street","Market Street","Martin Luther King Boulevard","Martin Luther King Drive","Martin Luther King Jr Boulevard","Memorial Drive","Middle Street","Mill Road","Mill Street","Monroe Street","Mulberry Street","N 1st Street","N 2nd Street","N 3rd Street","N 4th Street","N 5th Street","N 6th Street","N 7th Street","N 8th Street","N 9th Street","N Bridge Street","N Broad Street","N Broadway","N Broadway Street","N Cedar Street","N Center Street","N Central Avenue","N Chestnut Street","N Church Street","N College Street","N Court Street","N Division Street","N East Street","N Elm Street","N Franklin Street","N Front Street","N Harrison Street","N High Street","N Jackson Street","N Jefferson Street","N Lincoln Street","N Locust Street","N Main","N Main Avenue","N Main Street","N Maple Street","N Market Street","N Monroe Street","N Oak Street","N Park Street","N Pearl Street","N Pine Street","N Poplar Street","N Railroad Street","N State Street","N Union Street","N Walnut Street","N Washington Avenue","N Washington Street","N Water Street","North Avenue","North Road","North Street","Oak Avenue","Oak Street","Old State Road","Park Avenue","Park Drive","Park Street","Pearl Street","Pennsylvania Avenue","Pine Street","Pleasant Street","Poplar Street","Post Road","Prospect Avenue","Prospect Street","Railroad Avenue","Railroad Street","Ridge Road","River Road","River Street","Riverside Avenue","Riverside Drive","S 10th Street","S 14th Street","S 1st Avenue","S 1st Street","S 2nd Street","S 3rd Street","S 4th Street","S 5th Street","S 6th Street","S 7th Street","S 8th Street","S 9th Street","S Bridge Street","S Broad Street","S Broadway","S Broadway Street","S Center Street","S Central Avenue","S Chestnut Street","S Church Street","S College Street","S Division Street","S East Street","S Elm Street","S Franklin Street","S Front Street","S Grand Avenue","S High Street","S Jackson Street","S Jefferson Street","S Lincoln Street","S Main","S Main Avenue","S Main Street","S Maple Street","S Market Street","S Mill Street","S Monroe Street","S Oak Street","S Park Street","S Pine Street","S Railroad Street","S State Street","S Union Street","S Walnut Street","S Washington Avenue","S Washington Street","S Water Street","S West Street","School Street","Skyline Drive","South Avenue","South Street","Spring Street","Springfield Road","Spruce Street","State Avenue","State Line Road","State Road","State Street","Sycamore Street","Third Street","Union Avenue","Union Street","University Avenue","University Drive","Valley Road","Veterans Memorial Drive","Veterans Memorial Highway","Vine Street","W 10th Street","W 11th Street","W 12th Street","W 14th Street","W 1st Street","W 2nd Street","W 3rd Street","W 4th Avenue","W 4th Street","W 5th Street","W 6th Avenue","W 6th Street","W 7th Street","W 8th Street","W 9th Street","W Bridge Street","W Broad Street","W Broadway","W Broadway Avenue","W Broadway Street","W Center Street","W Central Avenue","W Chestnut Street","W Church Street","W Division Street","W Elm Street","W Franklin Street","W Front Street","W Grand Avenue","W High Street","W Jackson Street","W Jefferson Street","W Lake Street","W Main","W Main Street","W Maple Street","W Market Street","W Monroe Street","W North Street","W Oak Street","W Park Street","W Pine Street","W River Road","W South Street","W State Street","W Union Street","W Walnut Street","W Washington Avenue","W Washington Street","Walnut Street","Washington Avenue","Washington Boulevard","Washington Road","Washington Street","Water Street","West Avenue","West Road","West Street","Western Avenue","Willow Street"];var u=["{{person.firstName}} {{location.street_suffix}}","{{person.lastName}} {{location.street_suffix}}","{{location.street_name}}"];var f={city_pattern:a,county:o,postcode_by_state:S,street_name:m,street_pattern:u},l=f;var W={title:"English (United States)",code:"en_US",country:"US",language:"en",endonym:"English (United States)",dir:"ltr",script:"Latn"},s=W;var d={generic:[{value:"{{person.last_name.generic}}",weight:95},{value:"{{person.last_name.generic}}-{{person.last_name.generic}}",weight:5}]};var A={last_name_pattern:d},h=A;var x=["201","202","203","205","206","207","208","209","210","212","213","214","215","216","217","218","219","224","225","227","228","229","231","234","239","240","248","251","252","253","254","256","260","262","267","269","270","276","281","283","301","302","303","304","305","307","308","309","310","312","313","314","315","316","317","318","319","320","321","323","330","331","334","336","337","339","347","351","352","360","361","386","401","402","404","405","406","407","408","409","410","412","413","414","415","417","419","423","424","425","434","435","440","443","445","464","469","470","475","478","479","480","484","501","502","503","504","505","507","508","509","510","512","513","515","516","517","518","520","530","540","541","551","557","559","561","562","563","564","567","570","571","573","574","580","585","586","601","602","603","605","606","607","608","609","610","612","614","615","616","617","618","619","620","623","626","630","631","636","641","646","650","651","660","661","662","667","678","682","701","702","703","704","706","707","708","712","713","714","715","716","717","718","719","720","724","727","731","732","734","737","740","754","757","760","763","765","770","772","773","774","775","781","785","786","801","802","803","804","805","806","808","810","812","813","814","815","816","817","818","828","830","831","832","835","843","845","847","848","850","856","857","858","859","860","862","863","864","865","870","872","878","901","903","904","906","907","908","909","910","912","913","914","915","916","917","918","919","920","925","928","931","936","937","940","941","947","949","952","954","956","959","970","971","972","973","975","978","979","980","984","985","989"];var C=["201","202","203","205","206","207","208","209","210","212","213","214","215","216","217","218","219","224","225","227","228","229","231","234","239","240","248","251","252","253","254","256","260","262","267","269","270","276","281","283","301","302","303","304","305","307","308","309","310","312","313","314","315","316","317","318","319","320","321","323","330","331","334","336","337","339","347","351","352","360","361","386","401","402","404","405","406","407","408","409","410","412","413","414","415","417","419","423","424","425","434","435","440","443","445","464","469","470","475","478","479","480","484","501","502","503","504","505","507","508","509","510","512","513","515","516","517","518","520","530","540","541","551","557","559","561","562","563","564","567","570","571","573","574","580","585","586","601","602","603","605","606","607","608","609","610","612","614","615","616","617","618","619","620","623","626","630","631","636","641","646","650","651","660","661","662","667","678","682","701","702","703","704","706","707","708","712","713","714","715","716","717","718","719","720","724","727","731","732","734","737","740","754","757","760","763","765","770","772","773","774","775","781","785","786","801","802","803","804","805","806","808","810","812","813","814","815","816","817","818","828","830","831","832","835","843","845","847","848","850","856","857","858","859","860","862","863","864","865","870","872","878","901","903","904","906","907","908","909","910","912","913","914","915","916","917","918","919","920","925","928","931","936","937","940","941","947","949","952","954","956","959","970","971","972","973","975","978","979","980","984","985","989"];var p={area_code:x,exchange_code:C},v=p;var c={internet:i,location:l,metadata:s,person:h,phone_number:v},b= exports.a =c;var re=new (0, _chunkZKNYQOPPcjs.n)({locale:[b,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = b; exports.b = re;
