"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var r=["+351 91#######","+351 93#######","+351 96#######"];var U={formats:r},i=U;var t=["amarelo","amarelo-can\xE1rio","ameixa","azul","azul-claro","azul-cobalto","azul-marinho","azul-royal","azure","bege","bord\xF4","branco","bronzeado","carmesim","castanho","cer\xFAleo","ciano","cinza-ard\xF3sia","cinza-chumbo","cinza-prata","cinzento","cobre","dourado","esmeralda","f\xFAcsia","laranja","lavanda","lima","lim\xE3o","magenta","malva","marfim","marrom","ocre","orqu\xEDdea","ouro","prata","preto","p\xEAssego","p\xFArpura","rosa","rosa-beb\xEA","rosa-choque","roxo","safira","salm\xE3o","siena","s\xE9pia","terracota","tomate","turquesa","verde","verde-abacate","verde-esmeralda","verde-mar","verde-menta","verde-musgo","vermelho","vermelho-cereja","vermelho-escarlate","vermelho-rubi","vermelho-tomate","vermelho-vivo","violeta","\xE2mbar","\xEDndigo"];var X={human:t},n=X;var l=["Ar Livre","Autom\xF3veis","Beb\xE9","Beleza","Brinquedos","Casa","Computadores","Crian\xE7as","Desporto","Electr\xF3nica","Ferramentas","Filmes","Industrial","Jardim","Jogos","J\xF3ias","Livros","Mercearia","M\xFAsica","Roupas","Sapatos","Sa\xFAde"];var s={adjective:["Artesanal","Ergon\xF3mico","Fant\xE1stico","Feito \xE0 M\xE3o","Gen\xE9rico","Impressionante","Incr\xEDvel","Inteligente","Licenciado","Linda","Lustroso","Pequeno","Pr\xE1tico","Refinado","R\xFAstico","Saboroso","Sem Marca"],material:["Algod\xE3o","A\xE7o","Bet\xE3o","Borracha","Congelado","Fresco","Granito","Madeira","Metal","Pl\xE1stico","Suave"],product:["Atum","Bacon","Batatas Fritas","Bicicleta","Bola","Cadeira","Cal\xE7as","Camisa","Carro","Chap\xE9u","Computador","Frango","Luvas","Mesa","Peixe","Pizza","Queijo","Rato","Sabonete","Salada","Salsichas","Sapatos","Teclado","Toalhas"]};var K={department:l,product_name:s},m=K;var u=["EI","LDA","SA","SCR","ULTDA"];var d=["{{person.last_name.generic}} e {{person.last_name.generic}}","{{person.last_name.generic}} {{company.legal_entity_type}}"];var Z={legal_entity_type:u,name_pattern:d},c=Z;var p={wide:["Abril","Agosto","Dezembro","Fevereiro","Janeiro","Julho","Junho","Maio","Mar\xE7o","Novembro","Outubro","Setembro"],abbr:["Abr","Ago","Dez","Fev","Jan","Jul","Jun","Mai","Mar","Nov","Out","Set"]};var f={wide:["Domingo","Quarta","Quinta","Segunda","Sexta","S\xE1bado","Ter\xE7a"],abbr:["Dom","Qua","Qui","Seg","Sex","S\xE1b","Ter"]};var W={month:p,weekday:f},g=W;var M=["biz","com","com.pt","eu","gov.pt","info","name","net","org","org.pt","pt"];var b=["aeiou.pt","gmail.com","hotmail.com","live.com","mail.pt","outlook.com","portugalmail.pt","sapo.pt","yahoo.com"];var Y={domain_suffix:M,free_email:b},S=Y;var v=["####","###","##","#"];var C=["Abrantes","Agualva-Cac\xE9m","\xC1gueda","Albufeira","Alc\xE1cer do Sal","Alcoba\xE7a","Alfena","Almada","Almeirim","Amadora","Amarante","Amora","Anadia","Angra do Hero\xEDsmo","Aveiro","Barcelos","Barreiro","Beja","Braga","Bragan\xE7a","Caldas da Rainha","C\xE2mara de Lobos","Cani\xE7o","Cantanhede","Cartaxo","Castelo Branco","Chaves","Coimbra","Costa da Caparica","Covilh\xE3","Elvas","Entroncamento","Ermesinde","Esmoriz","Espinho","Esposende","Estarreja","Estremoz","\xC9vora","Fafe","Faro","F\xE1tima","Felgueiras","Fi\xE3es","Figueira da Foz","Freamunde","Funchal","Fund\xE3o","Gafanha da Nazar\xE9","Gandra","Gondomar","Gouveia","Guarda","Guimar\xE3es","Horta","\xCDlhavo","Lagoa","Lagos","Lamego","Leiria","Lisbon","Lixa","Loul\xE9","Loures","Lourosa","Macedo de Cavaleiros","Machico","Maia","Mangualde","Marco de Canaveses","Marinha Grande","Matosinhos","Mealhada","M\xEAda","Miranda do Douro","Mirandela","Montemor-o-Novo","Montijo","Moura","Odivelas","Olh\xE3o da Restaura\xE7\xE3o","Oliveira de Azem\xE9is","Oliveira do Bairro","Oliveira do Hospital","Our\xE9m","Ovar","Pa\xE7os de Ferreira","Paredes","Penafiel","Peniche","Peso da R\xE9gua","Pinhel","Pombal","Ponta Delgada","Ponte de Sor","Portalegre","Portim\xE3o","Porto","P\xF3voa de Santa Iria","P\xF3voa de Varzim","Praia da Vit\xF3ria","Quarteira","Queluz","Rebordosa","Reguengos de Monsaraz","Ribeira Grande","Rio Maior","Rio Tinto","Sabugal","Sacav\xE9m","Santa Comba D\xE3o","Santa Cruz","Santa Maria da Feira","Santana","Santar\xE9m","Santiago do Cac\xE9m","Santo Tirso","S\xE3o Jo\xE3o da Madeira","S\xE3o Mamede de Infesta","S\xE3o Salvador de Lordelo","Seia","Seixal","Serpa","Set\xFAbal","Silves","Sines","Tarouca","Tavira","Tomar","Tondela","Torres Novas","Torres Vedras","Trancoso","Trofa","Valbom","Vale de Cambra","Valongo","Valpa\xE7os","Vendas Novas","Viana do Castelo","Vila Baleira (a.k.a. Porto Santo)","Vila do Conde","Vila Franca de Xira","Vila Nova de Famalic\xE3o","Vila Nova de Foz C\xF4a","Vila Nova de Gaia","Vila Nova de Santo Andr\xE9","Vila Real","Vila Real de Santo Ant\xF3nio","Viseu","Vizela"];var A=["{{location.city_name}}"];var h=null;var L=null;var P=["\xC1frica do Sul","\xC1ustria","\xCDndia","Afeganist\xE3o","Alb\xE2nia","Alemanha","Andorra","Angola","Anguila","Ant\xE1rtida","Ant\xEDgua e Barbuda","Antilhas Neerlandesas","Ar\xE1bia Saudita","Arg\xE9lia","Argentina","Arm\xE9nia","Aruba","Austr\xE1lia","Azerbaij\xE3o","B\xE9lgica","B\xF3snia e Herzegovina","Baamas","Bangladesh","Bar\xE9m","Barbados","Belize","Benim","Bermudas","Bielorr\xFAssia","Birm\xE2nia","Bol\xEDvia","Botsuana","Brasil","Brunei","Bulg\xE1ria","Burundi","Burquina Faso","But\xE3o","Cabo Verde","Camar\xF5es","Camboja","Canad\xE1","Catar","Cazaquist\xE3o","Chade","Chile","China","Chipre","Col\xF4mbia","Comores","Congo-Brazzaville","Congo-Kinshasa","Coreia do Norte","Coreia do Sul","Costa Rica","Costa do Marfim","Cro\xE1cia","Cuba","Dinamarca","Dom\xEDnica","Egito","Emirados \xC1rabes Unidos","Equador","Eritreia","Eslov\xE1quia","Eslov\xE9nia","Espanha","Est\xF3nia","Estados Unidos","Eti\xF3pia","Ilhas Faro\xE9","Fiji","Filipinas","Finl\xE2ndia","Fran\xE7a","G\xE2mbia","Gab\xE3o","Gana","Ge\xF3rgia","Ilhas Ge\xF3rgia do Sul e Sandwich do Sul","Gibraltar","Gr\xE9cia","Granada","Gronel\xE2ndia","Guadalupe","Guam","Guatemala","Guiana","Guiana Francesa","Guin\xE9","Guin\xE9 Equatorial","Guin\xE9-Bissau","Haiti","Honduras","Hong Kong","Hungria","I\xE9men","Ilha Bouvet","Ilha Norfolk","Ilha do Natal","Ilhas Caim\xE3o","Ilhas Cook","Ilhas Falkland","Ilhas Heard e McDonald","Ilhas Marshall","Ilhas Menores Distantes dos Estados Unidos","Ilhas Salom\xE3o","Ilhas Turcas e Caicos","Ilhas Virgens Americanas","Ilhas Virgens Brit\xE2nicas","Ilhas dos Cocos","Indon\xE9sia","Ir\xE3o","Iraque","Irlanda","Isl\xE2ndia","Israel","It\xE1lia","Jamaica","Jap\xE3o","Djibouti","Jord\xE2nia","Iugosl\xE1via","Kuwait","L\xEDbano","L\xEDbia","Laos","Lesoto","Let\xF3nia","Lib\xE9ria","Liechtenstein","Litu\xE2nia","Luxemburgo","M\xE9xico","M\xF3naco","Macau","Maced\xF3nia do Norte","Madag\xE1scar","Mal\xE1sia","Malawi","Maldivas","Mali","Malta","Ilhas Marianas do Norte","Marrocos","Martinica","Maur\xEDcia","Maurit\xE2nia","Mayotte","Estados Federados da Micron\xE9sia","Mo\xE7ambique","Mold\xE1via","Mong\xF3lia","Montserrat","N\xEDger","Nam\xEDbia","Nauru","Nepal","Nicar\xE1gua","Nig\xE9ria","Niue","Noruega","Nova Caled\xF3nia","Nova Zel\xE2ndia","Om\xE3","Pa\xEDses Baixos","Palau","Panam\xE1","Papua-Nova Guin\xE9","Paquist\xE3o","Paraguai","Peru","Pitcairn","Pol\xF3nia","Polin\xE9sia Francesa","Porto Rico","Portugal","Qu\xE9nia","Quirguist\xE3o","Quirib\xE1ti","R\xFAssia","Reino Unido","Rep\xFAblica Centro-Africana","Rep\xFAblica Checa","Rep\xFAblica Dominicana","Reuni\xE3o","Rom\xE9nia","Ruanda","S\xE3o Crist\xF3v\xE3o e Neves","S\xE3o Marinho","Saint Pierre e Miquelon","S\xE3o Tom\xE9 e Pr\xEDncipe","S\xE3o Vicente e Granadinas","S\xEDria","El Salvador","Samoa","Samoa Americana","Santa Helena","Santa L\xFAcia","Saara Ocidental","Seicheles","Senegal","Serra Leoa","Singapura","Som\xE1lia","Sri Lanka","Su\xE9cia","Su\xED\xE7a","Essuat\xEDni","Sud\xE3o","Suriname","Svalbard e Jan Mayen","Tail\xE2ndia","Taiwan","Tajiquist\xE3o","Tanz\xE2nia","Territ\xF3rio Brit\xE2nico do Oceano \xCDndico","Territ\xF3rios Austrais Franceses","Timor Leste","Togo","Tokelau","Tonga","Trindade e Tobago","Tun\xEDsia","Turquemenist\xE3o","Turquia","Tuvalu","Ucr\xE2nia","Uganda","Uruguai","Uzbequist\xE3o","Vanuatu","Vaticano","Venezuela","Vietname","Wallis e Futuna","Z\xE2mbia","Zimbabu\xE9"];var B={cardinal:["Norte","Este","Sul","Oeste"],cardinal_abbr:["N","E","S","O"],ordinal:["Nordeste","Noroeste","Sudeste","Sodoeste"],ordinal_abbr:["NE","NO","SE","SO"]};var F=["####-###"];var x=["#Drt.","#Esq.","#Frt","R/C","Cv","#A","#B","#C","Bloco","Ed.","Ap.","Loja","Piso","Sub","Terr.","Slt.","Gar.","And.","Mor.","Escr."];var D=["A\xE7ores","Aveiro","Beja","Braga","Bragan\xE7a","Castelo Branco","Coimbra","\xC9vora","Faro","Guarda","Leiria","Lisboa","Madeira","Portalegre","Porto","Santar\xE9m","Set\xFAbal","Viana do Castelo","Vila Real","Viseu"];var E={normal:"{{location.street}} {{location.buildingNumber}}",full:"{{location.street}} {{location.buildingNumber}}-{{location.secondaryAddress}}"};var N=["{{location.street_prefix}} {{person.first_name.generic}} {{person.last_name.generic}}"];var R=["Acesso","Alameda","Avenida","Azinhaga","Bairro","Beco","Cal\xE7ada","Caminho","Escadas","Estrada","Jardim","Ladeira","Largo","Pra\xE7a","Praceta","Quinta","Rua","Travessa","Urbaniza\xE7\xE3o","Viela"];var $={building_number:v,city_name:C,city_pattern:A,city_prefix:h,city_suffix:L,country:P,direction:B,postcode:F,secondary_address:x,state:D,street_address:E,street_pattern:N,street_prefix:R},T=$;var aa={title:"Portuguese (Portugal)",code:"pt_PT",country:"PT",language:"pt",endonym:"Portugu\xEAs (Portugal)",dir:"ltr",script:"Latn"},V=aa;var G={generic:["Adriana","Afonso","Alexandra","Alexandre","Alice","Am\xE9lia","Ana","Andr\xE9","Ant\xF3nio","Ariana","Artur","Aurora","Beatriz","Benedita","Benjamim","Bernardo","Bruna","Bruno","B\xE1rbara","Caetana","Camila","Carla","Carlos","Carlota","Carminho","Carmo","Carolina","Catarina","Cec\xEDlia","Clara","Constan\xE7a","Cristiano","C\xE9lia","C\xE9sar","Daniel","Daniela","David","Diana","Dinis","Diogo","Duarte","D\xE9bora","Edgar","Eduarda","Eduardo","Elias","Elisa","Ema","Emanuel","Em\xEDlia","Eva","Fabiana","Feliciano","Fernando","Filipa","Filipe","Flor","Francisca","Francisco","Frederica","Frederico","F\xE1bio","Gabriel","Gabriela","Gaspar","Gil","Gon\xE7alo","Guilherme","Gustavo","Helena","Henrique","Hugo","H\xE9lio","Igor","In\xEAs","Irina","Isabel","Isac","Ivan","Ivo","Jaime","Joana","Joaquim","Jorge","Josu\xE9","Jos\xE9","Jo\xE3o","Juliana","Julieta","J\xE9ssica","J\xFAlia","J\xFAlio","Lara","Laura","Leandro","Leonardo","Leonor","Let\xEDcia","Lia","Lorena","Louren\xE7o","Luana","Lucas","Luena","Luna","Lu\xEDs","Lu\xEDsa","Madalena","Mafalda","Manel","Manuel","Mara","Marcelo","Marco","Marcos","Margarida","Maria","Mariana","Marta","Martim","Mateus","Matias","Matilde","Mauro","Melissa","Mia","Micael","Miguel","Miriam","Mois\xE9s","M\xE1rcia","M\xE1rio","Nat\xE1lia","Nicole","Norberto","Nuno","N\xFAria","Of\xE9lia","Ol\xEDvia","Paula","Paulo","Pedro","Pilar","Rafael","Rafaela","Raquel","Raul","Renato","Ricardo","Rita","Roberto","Rodrigo","Romeu","Rosa","Rui","R\xFAben","Safira","Salvador","Samuel","Sandro","Santiago","Sara","Sebasti\xE3o","Sim\xE3o","Sofia","Soraia","S\xE9rgio","S\xEDlvia","Tatiana","Teresa","Tiago","Tom\xE1s","Tom\xE9","Valentim","Valentina","Valter","Vasco","Vera","Vicente","Vit\xF3ria","V\xE2nia","V\xEDtor","Xavier","\xC1urea","\xC2ngelo","\xC9rica","\xCDgor","\xCDris"],female:["Adriana","Alexandra","Alice","Am\xE9lia","Ana","Ariana","Aurora","Beatriz","Benedita","Bruna","B\xE1rbara","Caetana","Camila","Carla","Carlota","Carminho","Carmo","Carolina","Catarina","Cec\xEDlia","Clara","Constan\xE7a","C\xE9lia","Daniela","Diana","D\xE9bora","Eduarda","Elisa","Ema","Em\xEDlia","Eva","Fabiana","Filipa","Flor","Francisca","Frederica","Gabriela","Helena","In\xEAs","Irina","Isabel","Joana","Juliana","Julieta","J\xE9ssica","J\xFAlia","Lara","Laura","Leonor","Let\xEDcia","Lia","Lorena","Luana","Luena","Luna","Lu\xEDsa","Madalena","Mafalda","Mara","Margarida","Maria","Mariana","Marta","Matilde","Melissa","Mia","Miriam","M\xE1rcia","Nat\xE1lia","Nicole","N\xFAria","Of\xE9lia","Ol\xEDvia","Paula","Pilar","Rafaela","Raquel","Rita","Rosa","Safira","Sara","Sofia","Soraia","S\xEDlvia","Tatiana","Teresa","Valentina","Vera","Vit\xF3ria","V\xE2nia","\xC1urea","\xC9rica","\xCDris"],male:["Afonso","Alexandre","Andr\xE9","Ant\xF3nio","Artur","Benjamim","Bernardo","Bruno","Carlos","Cristiano","C\xE9sar","Daniel","David","Dinis","Diogo","Duarte","Edgar","Eduardo","Elias","Emanuel","Feliciano","Fernando","Filipe","Francisco","Frederico","F\xE1bio","Gabriel","Gaspar","Gil","Gon\xE7alo","Guilherme","Gustavo","Henrique","Hugo","H\xE9lio","Igor","Isac","Ivan","Ivo","Jaime","Joaquim","Jorge","Josu\xE9","Jos\xE9","Jo\xE3o","J\xFAlio","Leandro","Leonardo","Louren\xE7o","Lucas","Lu\xEDs","Manel","Manuel","Marcelo","Marco","Marcos","Martim","Mateus","Matias","Mauro","Micael","Miguel","Mois\xE9s","M\xE1rio","Norberto","Nuno","Paulo","Pedro","Rafael","Raul","Renato","Ricardo","Roberto","Rodrigo","Romeu","Rui","R\xFAben","Salvador","Samuel","Sandro","Santiago","Sebasti\xE3o","Sim\xE3o","S\xE9rgio","Tiago","Tom\xE1s","Tom\xE9","Valentim","Valter","Vasco","Vicente","V\xEDtor","Xavier","\xC2ngelo","\xCDgor"]};var I={generic:["Abreu","Albuquerque","Almeida","Alves","Amado","Amaral","Amorim","Andrade","Anjos","Antunes","Ara\xFAjo","Assun\xE7\xE3o","Azevedo","Baptista","Barbosa","Barros","Batista","Borges","Braga","Branco","Brito","Camacho","Campos","Cardoso","Carneiro","Carvalho","Castro","Coelho","Correia","Costa","Cruz","Cunha","Domingues","Esteves","Falc\xE3o","Faria","Fernandes","Ferreira","Fid\xE9lis","Figueiredo","Fonseca","Fraga","Freitas","Furtado","Garcia","Gaspar","Gomes","Gon\xE7alves","Guerreiro","Henriques","Jesus","Lacerda","Leal","Leite","Lima","Lopes","Loureiro","Louren\xE7o","Lourinho","Macedo","Machado","Magalh\xE3es","Maia","Mariz","Marques","Martins","Matias","Matos","Medeiros","Meireles","Melo","Mendes","Mesquita","Miranda","Monteiro","Moraes","Morais","Moreira","Mota","Moura","Nascimento","Neto","Neves","Nobre","Nogueira","Nunes","Oliva","Oliveira","Pacheco","Paiva","Peixoto","Pereira","Pimentel","Pinheiro","Pinho","Pinto","Pires","Queiroz","Ramos","Raposo","Reis","Ribeiro","Rocha","Rodrigues","Santos","Saraiva","Serra","Silva","Sim\xF5es","Soares","Sousa","S\xE1","Tavares","Teixeira","Torres","Valente","Vaz","Veiga","Vicente","Vieira","Xavier"]};var z={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var J=[{value:"{{person.firstName}} {{person.lastName}}",weight:9},{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1}];var _={generic:["Adv.","Adv.\xAA","Arq.","Arq.\xAA","Dr.","Dra.","Enf.","Enf.\xAA","Eng.\xAA","Eng.\xBA","Prof.","Prof.\xAA","Sr.","Sra.","T\xE9c.","T\xE9c.\xAA"],female:["Adv.\xAA","Arq.\xAA","Dra.","Enf.\xAA","Eng.\xAA","Prof.\xAA","Sra.","T\xE9c.\xAA"],male:["Adv.","Arq.","Dr.","Enf.","Eng.\xBA","Prof.","Sr.","T\xE9c."]};var y=null;var oa={first_name:G,last_name:I,last_name_pattern:z,name:J,prefix:_,suffix:y},q=oa;var O=["+351 2########","+351 91#######","+351 92#######","+351 93#######","+351 96#######"];var H=["+3512########","+35191#######","+35192#######","+35193#######","+35196#######"];var j=["2## ### ###","91# ### ###","92# ### ###","93# ### ###","96# ### ###"];var ea={human:O,international:H,national:j},Q=ea;var ra={format:Q},w=ra;var ia={cell_phone:i,color:n,commerce:m,company:c,date:g,internet:S,location:T,metadata:V,person:q,phone_number:w},k= exports.a =ia;var jo=new (0, _chunkZKNYQOPPcjs.n)({locale:[k,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = k; exports.b = jo;
