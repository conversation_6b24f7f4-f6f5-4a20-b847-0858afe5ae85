(node:28096) [DEP0005] DeprecationWarning: <PERSON><PERSON><PERSON>() is deprecated due to security and usability issues. Please use the Buffer.alloc(), Buffer.allocUnsafe(), or Buffer.from() methods instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
SQL Server connection pool error: ConnectionError: Cannot open server 'parkwizvms' requested by the login. Client with IP address '*************' is not allowed to access the server.  To enable access, use the Azure Management Portal or run sp_set_firewall_rule on the master database to create a firewall rule for this IP address or address range.  It may take up to five minutes for this change to take effect.
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\mssql\lib\tedious\connection-pool.js:85:17
    at Connection.onConnect (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:849:9)
    at Object.onceWrapper (node:events:633:26)
    at Connection.emit (node:events:518:28)
    at Connection.emit (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:970:18)
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:2369:18
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'ELOGIN',
  originalError: ConnectionError: Cannot open server 'parkwizvms' requested by the login. Client with IP address '*************' is not allowed to access the server.  To enable access, use the Azure Management Portal or run sp_set_firewall_rule on the master database to create a firewall rule for this IP address or address range.  It may take up to five minutes for this change to take effect.
      at Login7TokenHandler.onErrorMessage (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\token\handler.js:186:19)
      at Readable.<anonymous> (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\token\token-stream-parser.js:19:33)
      at Readable.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)
      at Readable.push (node:internal/streams/readable:393:5)
      at nextAsync (node:internal/streams/from:194:22)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'ELOGIN'
  }
}
Failed to initialize database connection pool: ConnectionError: Cannot open server 'parkwizvms' requested by the login. Client with IP address '*************' is not allowed to access the server.  To enable access, use the Azure Management Portal or run sp_set_firewall_rule on the master database to create a firewall rule for this IP address or address range.  It may take up to five minutes for this change to take effect.
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\mssql\lib\tedious\connection-pool.js:85:17
    at Connection.onConnect (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:849:9)
    at Object.onceWrapper (node:events:633:26)
    at Connection.emit (node:events:518:28)
    at Connection.emit (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:970:18)
    at C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\connection.js:2369:18
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'ELOGIN',
  originalError: ConnectionError: Cannot open server 'parkwizvms' requested by the login. Client with IP address '*************' is not allowed to access the server.  To enable access, use the Azure Management Portal or run sp_set_firewall_rule on the master database to create a firewall rule for this IP address or address range.  It may take up to five minutes for this change to take effect.
      at Login7TokenHandler.onErrorMessage (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\token\handler.js:186:19)
      at Readable.<anonymous> (C:\inetpub\wwwroot\PWVMS\backend\node_modules\tedious\lib\token\token-stream-parser.js:19:33)
      at Readable.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)
      at Readable.push (node:internal/streams/readable:393:5)
      at nextAsync (node:internal/streams/from:194:22)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'ELOGIN'
  }
}
