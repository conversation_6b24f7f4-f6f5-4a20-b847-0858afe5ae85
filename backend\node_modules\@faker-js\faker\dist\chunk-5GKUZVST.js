import{a as e}from"./chunk-KERBADJJ.js";import{n as t,o}from"./chunk-PC2QB7VM.js";var r=["082 ### ####","083 ### ####","085 ### ####","086 ### ####","087 ### ####","089 ### ####"];var L={formats:r},i=L;var n=["com","eu","ie","info","net"];var N={domain_suffix:n},a=N;var f=["{{location.city_prefix}} {{person.firstName}}{{location.city_suffix}}","{{location.city_prefix}} {{person.firstName}}","{{person.firstName}}{{location.city_suffix}}","{{person.last_name.generic}}{{location.city_suffix}}"];var m=["Carlow","Cavan","Clare","Cork","Donegal","Dublin","Galway","Kerry","Kildare","Kilkenny","<PERSON><PERSON>","Leitrim","Limerick","Longford","Louth","Mayo","<PERSON><PERSON>","Monaghan","Offaly","Roscommon","Sligo","Tipperary","Waterford","Westmeath","Wexford","Wicklow"];var p=["A## ****","D## ****","E## ****","F## ****","H## ****","K## ****","N## ****","P## ****","R## ****","T## ****","V## ****","W## ****","X## ****","Y## ****"];var l=["{{person.firstName}} {{location.street_suffix}}","{{person.lastName}} {{location.street_suffix}}"];var P={city_pattern:f,county:m,postcode:p,street_pattern:l},s=P;var b={title:"English (Ireland)",code:"en_IE",country:"IE",language:"en",endonym:"English (Ireland)",dir:"ltr",script:"Latn"},c=b;var u={generic:[{value:"{{person.last_name.generic}}",weight:95},{value:"{{person.last_name.generic}}-{{person.last_name.generic}}",weight:5}]};var I={last_name_pattern:u},d=I;var x=["01 #######","021 #######","022 #######","023 #######","024 #######","025 #######","026 #######","027 #######","028 #######","029 #######","0402 #######","0404 #######","041 #######","042 #######","043 #######","044 #######","045 #######","046 #######","047 #######","049 #######","0504 #######","0505 #######","051 #######","052 #######","053 #######","056 #######","057 #######","058 #######","059 #######","061 #######","062 #######","063 #######","064 #######","065 #######","066 #######","067 #######","068 #######","069 #######","071 #######","074 #######","090 #######","091 #######","093 #######","094 #######","095 #######","096 #######","097 #######","098 #######","099 #######"];var y=["+3531#######","+35321#######","+35322#######","+35323#######","+35324#######","+35325#######","+35326#######","+35327#######","+35328#######","+35329#######","+353402#######","+353404#######","+35341#######","+35342#######","+35343#######","+35344#######","+35345#######","+35346#######","+35347#######","+35349#######","+353504#######","+353505#######","+35351#######","+35352#######","+35353#######","+35356#######","+35357#######","+35358#######","+35359#######","+35361#######","+35362#######","+35363#######","+35364#######","+35365#######","+35366#######","+35367#######","+35368#######","+35369#######","+35371#######","+35374#######","+35390#######","+35391#######","+35393#######","+35394#######","+35395#######","+35396#######","+35397#######","+35398#######","+35399#######"];var _=["(01) ### ####","(021) ### ####","(022) ### ####","(023) ### ####","(024) ### ####","(025) ### ####","(026) ### ####","(027) ### ####","(028) ### ####","(029) ### ####","(040) 2### ####","(040) 4### ####","(041) ### ####","(042) ### ####","(043) ### ####","(044) ### ####","(045) ### ####","(046) ### ####","47#######","(049) ### ####","504#######","505#######","(051) ### ####","(052) ### ####","(053) ### ####","(056) ### ####","(057) ### ####","(058) ### ####","(059) ### ####","(061) ### ####","(062) ### ####","(063) ### ####","(064) ### ####","(065) ### ####","(066) ### ####","(067) ### ####","(068) ### ####","(069) ### ####","(071) ### ####","(074) ### ####","(090) ### ####","(091) ### ####","(093) ### ####","(094) ### ####","(095) ### ####","(096) ### ####","(097) ### ####","(098) ### ####","(099) ### ####"];var E={human:x,international:y,national:_},h=E;var k={format:h},D=k;var w={cell_phone:i,internet:a,location:s,metadata:c,person:d,phone_number:D},g=w;var dt=new t({locale:[g,e,o]});export{g as a,dt as b};
