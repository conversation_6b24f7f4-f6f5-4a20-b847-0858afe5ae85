Environment variables loaded:
DB_SERVER: parkwizvms.database.windows.net
DB_NAME: ParkwizOps
DB_USER: hparkwiz
DB_PORT: 1433
🚀 Initializing Redis and services...
Server running on port \\.\pipe\4a16cf5a-050c-4010-b395-9c60a77c4c0e
✅ Redis client connected successfully
✅ Redis client ready for operations
🚀 Redis connection established successfully
✅ RedisService initialized successfully
✅ RealtimeService initialized successfully
✅ All services initialized successfully
Database connected: ParkwizOps
Database connected: ParkwizOps
Database connected: ParkwizOps
Database connected: ParkwizOps
❌ Cache miss - fetching dashboard summary from database
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
❌ Cache miss - fetching revenue by payment method from database
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
✅ Revenue by payment method cached for 60 seconds
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
✅ Dashboard summary cached for 60 seconds
Digital Pay - Getting only active configurations
Digital Pay - Getting only active configurations
❌ Cache miss - fetching dashboard summary from database
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
❌ Cache miss - fetching revenue by payment method from database
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
✅ Revenue by payment method cached for 60 seconds
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
✅ Dashboard summary cached for 60 seconds
✅ Dashboard summary served from Redis cache
✅ Revenue by payment method served from Redis cache
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
📅 Date Range (UTC): 2025-07-03T00:30:00.000Z to 2025-07-04T00:29:59.999Z
🇮🇳 Date Range (IST): 3/7/2025, 11:30:00 am to 4/7/2025, 11:29:59 am
